version: '3.8'

services:
  mongodb:
    image: mongo:6-jammy
    container_name: mongodb
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password123
      - MONGO_INITDB_DATABASE=admin
    volumes:
      - mongodb-data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js
    ports:
      - '27017:27017'
    restart: unless-stopped

volumes:
  mongodb-data:
    name: mongodb-data
